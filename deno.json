{"version": "1.4.0", "license": "MIT", "tasks": {"start": "deno run -A ./src/bot.ts", "dev": "deno run --env-file -A --watch ./src/bot.ts"}, "imports": {"@discordjs/builders": "npm:@discordjs/builders@^1.10.1", "@discordjs/rest": "npm:@discordjs/rest@^2.4.3", "@libsql/kysely-libsql": "npm:@libsql/kysely-libsql@^0.4.1", "@std/assert": "jsr:@std/assert@1", "discord-api-types": "npm:discord-api-types@^0.37.120", "discord.js": "npm:discord.js@^14.18.0", "kysely": "npm:kysely@^0.28.2", "mongoose": "npm:mongoose@^8.13.2"}}