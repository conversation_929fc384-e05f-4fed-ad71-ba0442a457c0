# HexaBot

A multipurpose Discord Bot that aims to replicate the functionality of other popular Discord Bots, such as moderation, music, logging, and more.

## Getting Started

If you wish to install and run HexaBot, follow these steps:
```sh
git clone https://github.com/ButterDebugger/HexaBot.git
cd HexaBot
npm install
npm start
```

### Environment Variables
Declare the environment variables on your system or inside a file named `.env` in the root of the project's directory.
```env
BOT_TOKEN = <Discord Bot Token>
```

## License

HexaBot is licensed under [``MIT License``](LICENSE).
