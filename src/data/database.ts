import mongoose, { Schema } from "mongoose";
import { MONGODB_URL } from "./config.ts";
import fs from "node:fs";
import process from "node:process";

// Open a client connection
await mongoose
	.connect(MONGODB_URL, {
		dbName: "discordBot"
	})
	.then(() => console.log("Connected successfully to server"));

// Guild Schema
export interface GuildModel {
	guildId: string;

	logging_channel: string;

	log_member_joins: boolean;
	log_member_leaves: boolean;
	log_message_deletion: boolean;
	log_message_edits: boolean;
	log_member_banned: boolean;
	log_member_unbanned: boolean;
	log_member_kicked: boolean;
	log_invite_added: boolean;
	log_invite_removed: boolean;

	use_sticky_roles: boolean;
	sticky_roles: string[];

	use_auto_roles: boolean;
	auto_roles: string[];

	owner_only: boolean;

	join_message_channel: string;
	join_message: string;
	join_direct_message: string;

	leave_message_channel: string;
	leave_message: string;

	counting_channel: string;
	counting_topic: string;
}
const guildSchema = new Schema<GuildModel>({
	guildId: { type: String, required: true },

	logging_channel: { type: String, default: "" }, // Default: null

	log_member_joins: { type: Boolean, default: false }, // Default: false
	log_member_leaves: { type: Boolean, default: false }, // Default: false
	log_message_deletion: { type: Boolean, default: false }, // Default: false
	log_message_edits: { type: Boolean, default: false }, // Default: false
	log_member_banned: { type: Boolean, default: false }, // Default: false
	log_member_unbanned: { type: Boolean, default: false }, // Default: false
	log_member_kicked: { type: Boolean, default: false }, // Default: false
	log_invite_added: { type: Boolean, default: false }, // Default: false
	log_invite_removed: { type: Boolean, default: false }, // Default: false

	use_sticky_roles: { type: Boolean, default: false }, // Default: false
	sticky_roles: Array, // Default: []

	use_auto_roles: { type: Boolean, default: false }, // Default: false
	auto_roles: Array, // Default: []

	owner_only: { type: Boolean, default: false }, // Default: false

	join_message_channel: { type: String, default: "" }, // Default: null
	join_message: { type: String, default: "" }, // Default: null
	join_direct_message: { type: String, default: "" }, // Default: null

	leave_message_channel: { type: String, default: "" }, // Default: null
	leave_message: { type: String, default: "" }, // Default: null

	counting_channel: { type: String, default: "" }, // Default: null
	counting_topic: { type: String, default: "" } // Default: null

	// counting_emoji_style: "normal",
	// counting_number_style: "normal",
});

const Guild = mongoose.model("Guild", guildSchema);

export async function getGuild(guildId: string) {
	const guild = await Guild.findOne({ guildId: guildId }).exec();

	// If guild doesn't exist, create it
	if (guild == null) {
		const newGuild = new Guild({ guildId: guildId });
		await newGuild.save();
		return newGuild;
	}

	return guild;
}

export async function resetGuildToDefaults(
	guildId: string,
	keyFilter: (key: string) => boolean = () => true
) {
	const guild = await getGuild(guildId);

	for (const key in guild.schema.paths) {
		if (key.startsWith("_")) continue;
		if (key === "guildId") continue;

		// @ts-ignore: This is a defined value
		let defaultValue = guild.schema.paths[key].defaultValue;
		if (typeof defaultValue === "function") defaultValue = defaultValue();
		if (typeof defaultValue === "undefined") continue;
		if (!keyFilter(key)) continue;

		guild.set(key, defaultValue);
	}

	guild.save();
}

export async function migrateConfigs() {
	const configsPath = `${process.cwd()}/storage/configs`;
	const defaultConfig = JSON.parse(
		// @ts-ignore: It works
		fs.readFileSync(`${process.cwd()}/storage/defaultconfig.json`)
	);

	for (const filename of fs.readdirSync(configsPath)) {
		const config = JSON.parse(
			// @ts-ignore: It works
			fs.readFileSync(`${configsPath}/${filename}`)
		);

		const id = filename.replace(".json", "");
		const guild = await getGuild(id);

		for (const key in defaultConfig) {
			if (!config[key]) {
				config[key] = defaultConfig[key];
			} else {
				guild.set(key, config[key]);
			}
		}

		await guild.save();
	}
}
