import { Kysely, ParseJ<PERSON>NResultsPlugin } from "kysely";
import { LibsqlDialect } from "@libsql/kysely-libsql";
import { DATABASE_URL } from "./config.ts";

// Database schema types
export interface GuildTable {
	guild_id: string;

	logging_channel: string;

	log_member_joins: number;
	log_member_leaves: number;
	log_message_deletion: number;
	log_message_edits: number;
	log_member_banned: number;
	log_member_unbanned: number;
	log_member_kicked: number;
	log_invite_added: number;
	log_invite_removed: number;

	use_sticky_roles: boolean;
	sticky_roles: string[];

	use_auto_roles: boolean;
	auto_roles: string[];

	owner_only: boolean;

	join_message_channel: string;
	join_message: string;
	join_direct_message: string;

	leave_message_channel: string;
	leave_message: string;

	counting_channel: string;
	counting_topic: string;
}

export interface Database {
	guilds: GuildTable;
}

// Create database connection
export const db = new Kysely<Database>({
	dialect: new LibsqlDialect({
		url: DATABASE_URL,
		// syncUrl: database_sync_url,
		// authToken: database_auth_Token,
		syncInterval: 60 // Sync every minute
	}),
	plugins: [new ParseJSONResultsPlugin()]
});

// Initialize database tables
export async function initializeDatabase() {
	try {
		await db.schema
			.createTable("guilds")
			.ifNotExists()
			.addColumn("guild_id", "text", (col) => col.primaryKey())
			.addColumn("logging_channel", "text", (col) => col.defaultTo(""))
			.addColumn("log_member_joins", "integer", (col) => col.defaultTo(0))
			.addColumn("log_member_leaves", "integer", (col) =>
				col.defaultTo(0)
			)
			.addColumn("log_message_deletion", "integer", (col) =>
				col.defaultTo(0)
			)
			.addColumn("log_message_edits", "integer", (col) =>
				col.defaultTo(0)
			)
			.addColumn("log_member_banned", "integer", (col) =>
				col.defaultTo(0)
			)
			.addColumn("log_member_unbanned", "integer", (col) =>
				col.defaultTo(0)
			)
			.addColumn("log_member_kicked", "integer", (col) =>
				col.defaultTo(0)
			)
			.addColumn("log_invite_added", "integer", (col) => col.defaultTo(0))
			.addColumn("log_invite_removed", "integer", (col) =>
				col.defaultTo(0)
			)
			.addColumn("use_sticky_roles", "boolean", (col) =>
				col.defaultTo(false)
			)
			.addColumn("sticky_roles", "text", (col) => col.defaultTo("[]"))
			.addColumn("use_auto_roles", "boolean", (col) =>
				col.defaultTo(false)
			)
			.addColumn("auto_roles", "text", (col) => col.defaultTo("[]"))
			.addColumn("owner_only", "boolean", (col) => col.defaultTo(false))
			.addColumn("join_message_channel", "text", (col) =>
				col.defaultTo("")
			)
			.addColumn("join_message", "text", (col) => col.defaultTo(""))
			.addColumn("join_direct_message", "text", (col) =>
				col.defaultTo("")
			)
			.addColumn("leave_message_channel", "text", (col) =>
				col.defaultTo("")
			)
			.addColumn("leave_message", "text", (col) => col.defaultTo(""))
			.addColumn("counting_channel", "text", (col) => col.defaultTo(""))
			.addColumn("counting_topic", "text", (col) => col.defaultTo(""))
			.execute();

		console.log("Database initialized successfully");
	} catch (error) {
		console.error("Failed to initialize database:", error);
		throw error;
	}
}

export async function getGuild(guildId: string): Promise<GuildTable> {
	const guild = await db
		.selectFrom("guilds")
		.selectAll()
		.where("guild_id", "=", guildId)
		.executeTakeFirst();

	// If guild doesn't exist, create it with defaults
	if (!guild) {
		const newGuild: GuildTable = {
			guild_id: guildId,
			logging_channel: "",
			log_member_joins: 0,
			log_member_leaves: 0,
			log_message_deletion: 0,
			log_message_edits: 0,
			log_member_banned: 0,
			log_member_unbanned: 0,
			log_member_kicked: 0,
			log_invite_added: 0,
			log_invite_removed: 0,
			use_sticky_roles: false,
			sticky_roles: [],
			use_auto_roles: false,
			auto_roles: [],
			owner_only: false,
			join_message_channel: "",
			join_message: "",
			join_direct_message: "",
			leave_message_channel: "",
			leave_message: "",
			counting_channel: "",
			counting_topic: ""
		};

		await db.insertInto("guilds").values(newGuild).execute();

		return newGuild;
	}

	return guild;
}

export async function resetGuildToDefaults(
	guildId: string,
	keyFilter: (key: string) => boolean = () => true
) {
	const defaultValues: Omit<GuildTable, "guild_id"> = {
		logging_channel: "",
		log_member_joins: 0,
		log_member_leaves: 0,
		log_message_deletion: 0,
		log_message_edits: 0,
		log_member_banned: 0,
		log_member_unbanned: 0,
		log_member_kicked: 0,
		log_invite_added: 0,
		log_invite_removed: 0,
		use_sticky_roles: false,
		sticky_roles: [],
		use_auto_roles: false,
		auto_roles: [],
		owner_only: false,
		join_message_channel: "",
		join_message: "",
		join_direct_message: "",
		leave_message_channel: "",
		leave_message: "",
		counting_channel: "",
		counting_topic: ""
	};

	// Build update object with proper typing
	const updateEntries = Object.entries(defaultValues).filter(([key]) =>
		keyFilter(key)
	);

	if (updateEntries.length > 0) {
		const updateValues = Object.fromEntries(
			updateEntries
		) as Partial<GuildTable>;

		await db
			.updateTable("guilds")
			.set(updateValues)
			.where("guild_id", "=", guildId)
			.execute();
	}
}

// Note: migrateConfigs function removed as it was specific to the old file-based config system
// If you need to migrate data, create a new migration function specific to your needs
