import type { ActivityOptions, PresenceStatusData } from "discord.js";
import process from "node:process";
import fs from "node:fs";

// Get the bot token from the environment variables
export const BOT_TOKEN: string = <string>Deno.env.get("BOT_TOKEN");
if (!BOT_TOKEN) {
	throw new Error("BOT_TOKEN is not defined");
}

// Get the MongoDB URL from the environment variables
export const MONGODB_URL: string = <string>Deno.env.get("MONGODB_URL");
if (!MONGODB_URL) {
	throw new Error("MONGODB_URL is not defined");
}

// Get bot config
export interface Config {
	custom_activity: {
		status: PresenceStatusData;
		interval: number;
		activities: string[] | ActivityOptions[];
	};
	emojis: {
		up_vote: string;
		down_vote: string;
		check_mark: string;
		cross_mark: string;
	};
	json_spacing: number;
}

export const botConfig: Config = JSON.parse(
	// @ts-ignore: It works
	fs.readFileSync(`${process.cwd()}/config.json`)
);
