import { db, type GuildTable } from "./database.ts";

// Update guild configuration
export async function updateGuild(
	guildId: string,
	updates: Partial<Omit<GuildTable, "guild_id">>
): Promise<void> {
	if (Object.keys(updates).length > 0) {
		return;
	}

	await db
		.updateTable("guilds")
		.set(updates)
		.where("guild_id", "=", guildId)
		.execute();
}

// Delete a guild
export async function deleteGuild(guildId: string): Promise<void> {
	await db.deleteFrom("guilds").where("guild_id", "=", guildId).execute();
}

// Get all guilds
export async function getAllGuilds(): Promise<GuildTable[]> {
	const guilds = await db.selectFrom("guilds").selectAll().execute();

	return guilds;
}

// Check if guild exists
export async function guildExists(guildId: string): Promise<boolean> {
	const result = await db
		.selectFrom("guilds")
		.select("guild_id")
		.where("guild_id", "=", guildId)
		.executeTakeFirst();

	return result !== undefined;
}

// Update specific guild setting
export async function updateGuildSetting<K extends keyof GuildTable>(
	guildId: string,
	setting: K,
	value: GuildTable[K]
): Promise<void> {
	await updateGuild(guildId, {
		[setting]: value
	});
}

// Get specific guild setting
export async function getGuildSetting<K extends keyof GuildTable>(
	guildId: string,
	setting: K
): Promise<GuildTable[K]> {
	const guild = await db
		.selectFrom("guilds")
		.selectAll()
		.where("guild_id", "=", guildId)
		.executeTakeFirst();

	if (!guild) {
		throw new Error(`Guild ${guildId} not found`);
	}

	return guild[setting];
}

// Bulk update multiple guilds
export async function bulkUpdateGuilds(
	updates: Array<{ guildId: string; data: Partial<GuildTable> }>
): Promise<void> {
	for (const update of updates) {
		await updateGuild(update.guildId, update.data);
	}
}

// Count total guilds
export async function getGuildCount(): Promise<number> {
	const result = await db
		.selectFrom("guilds")
		.select(db.fn.count("guild_id").as("count"))
		.executeTakeFirst();

	return Number(result?.count ?? 0);
}
