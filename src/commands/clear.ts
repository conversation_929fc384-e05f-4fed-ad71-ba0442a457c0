import {
	ChannelType,
	type ChatInputCommandInteraction,
	type Client,
	MessageFlags,
	SlashCommandBuilder
} from "discord.js";

export const data = new SlashCommandBuilder()
	.setName("clear")
	.setDescription("Bulk delete messages from a channel")
	.addIntegerOption((option) =>
		option
			.setName("amount")
			.setDescription("The number of messages to delete")
			.setRequired(true)
			.setMinValue(1)
			.setMaxValue(100)
	);

export async function onCommand(
	_bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const { options } = interaction;

	const amount = options.getInteger("amount");

	if (
		interaction.channel === null ||
		interaction.channel.type !== ChannelType.GuildText
	) {
		interaction.reply({
			content: "Sorry, I can't bulk delete messages in this channel.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	if (amount === null) {
		interaction.reply({
			content: "Please provide an amount.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	await interaction.deferReply({
		// content: `Clearing ${amount} messages...`,
		flags: MessageFlags.Ephemeral
	});

	try {
		const messages = await interaction.channel.bulkDelete(amount, false);

		interaction.editReply({
			content: `Bulk deleted ${messages.size} ${
				messages.size > 1 ? "messages" : "message"
			}.`
		});
	} catch (error) {
		console.error("Failed to bulk delete messages.", error);

		interaction.editReply({
			content: `Sorry, I wasn't able to bulk delete messages.`
		});
	}
}
