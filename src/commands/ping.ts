import {
	Slash<PERSON>ommandBuilder,
	EmbedBuilder,
	type Client,
	type ChatInputCommandInteraction,
	MessageFlags
} from "discord.js";

export const data = new SlashCommandBuilder()
	.setName("ping")
	.setDescription("Ping the bot");

export function onCommand(
	bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const embed = new EmbedBuilder()
		.setColor(0x5865f2)
		.setTitle("Pong! 🏓")
		.setTimestamp()
		.setFooter({
			text: `Latency: ${bot.ws.ping}ms`
		});

	interaction.reply({
		embeds: [embed],
		flags: MessageFlags.Ephemeral
	});
}
