import {
	Slash<PERSON>ommandBuilder,
	user<PERSON>ention,
	EmbedBuilder,
	type Client,
	type ChatInputCommandInteraction,
	MessageFlags
} from "discord.js";
import { botConfig } from "../data/config.ts";

const { emojis } = botConfig;

export const data = new SlashCommandBuilder()
	.setName("poll")
	.setDescription("Create a poll")
	.addStringOption((option) =>
		option
			.setName("message")
			.setDescription("The message of the poll")
			.setRequired(true)
			.setMaxLength(1024)
	);

export async function onCommand(
	_bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const { options } = interaction;

	const message = options.getString("message");

	if (message === null) {
		interaction.reply({
			content: "Please provide a message.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	try {
		const embed = new EmbedBuilder()
			.setColor(0x00ae86)
			.setTitle("▬▬▬▬▬▬▬▬▬▬▬▬▬« Poll »▬▬▬▬▬▬▬▬▬▬▬▬▬")
			.addFields(
				{
					name: "Created By:",
					value: userMention(interaction.user.id)
				},
				{ name: "Description:", value: message }
			)
			.setTimestamp();

		const reply = await interaction.reply({
			embeds: [embed],
			fetchReply: true
		});

		await reply.react(emojis.up_vote).catch(console.error);
		await reply.react(emojis.down_vote).catch(console.error);
	} catch (error) {
		console.error("Something went wrong when creating a poll.", error);

		interaction.reply({
			content: "Sorry, I wasn't able to create the poll.",
			flags: MessageFlags.Ephemeral
		});
	}
}
