import {
	Slash<PERSON>ommandBuilder,
	EmbedBuilder,
	PermissionFlagsBits,
	type ChatInputCommandInteraction,
	type Client,
	MessageFlags,
	type HexColorString,
	TextChannel
} from "discord.js";

export const data = new SlashCommandBuilder()
	.setName("embed")
	.setDescription("Send an embed")
	.setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages)
	.addStringOption((option) =>
		option
			.setName("title")
			.setDescription("The title of the embed")
			.setMaxLength(256)
	)
	.addStringOption((option) =>
		option
			.setName("description")
			.setDescription("The description of the embed")
			.setMaxLength(4096)
	)
	.addStringOption((option) =>
		option
			.setName("color")
			.setDescription("The hex color of the embed")
			.setMinLength(7)
			.setMaxLength(7)
	)
	.addStringOption((option) =>
		option
			.setName("url")
			.setDescription("The url of the embed")
			.setMaxLength(2048)
	)
	.addStringOption((option) =>
		option
			.setName("footer")
			.setDescription("The footer of the embed")
			.setMaxLength(2048)
	);

export function onCommand(
	_bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const { options } = interaction;

	const title = options.getString("title");
	const description = options.getString("description");
	const color = options.getString("color");
	const url = options.getString("url");
	const footer = options.getString("footer");

	if (title === null && description === null && footer === null) {
		interaction.reply({
			content: "Please provide a title, description, or footer.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	if (interaction.channel instanceof TextChannel === false) {
		interaction.reply({
			content: "Sorry, I could not send the embed in this channel.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	const embed = new EmbedBuilder();

	if (typeof title === "string") {
		embed.setTitle(title);
	}
	if (typeof description === "string") {
		embed.setDescription(description);
	}
	if (typeof color === "string") {
		const colorRegex = /^#([0-9a-fA-F]{6})$/g;

		if (colorRegex.test(color)) {
			embed.setColor(color as HexColorString);
		} else {
			interaction.reply({
				content: "Invalid color.",
				flags: MessageFlags.Ephemeral
			});
			return;
		}
	}
	if (typeof url === "string") {
		try {
			embed.setURL(url);
		} catch (error) {
			console.error("Failed to set url in embed command.", error);

			interaction.reply({
				content: "Invalid url.",
				flags: MessageFlags.Ephemeral
			});
			return;
		}
	}
	if (typeof footer === "string") {
		embed.setFooter({
			text: footer
		});
	}

	interaction.channel
		.send({
			embeds: [embed]
		})
		.then(() => {
			interaction.reply({
				content: "Embed has been sent!",
				flags: MessageFlags.Ephemeral
			});
		})
		.catch(() => {
			interaction.reply({
				content: "Sorry, I wasn't able to send the embed.",
				flags: MessageFlags.Ephemeral
			});
		});
}
