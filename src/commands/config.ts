import {
	Slash<PERSON>ommandBuilder,
	PermissionFlagsBits,
	EmbedBuilder,
	ChannelType,
	inlineCode,
	channelMention,
	roleMention,
	type ChatInputCommandInteraction,
	type Client,
	MessageFlags
} from "discord.js";
import { getGuild, resetGuildToDefaults } from "../data/database.ts";

export const data = new SlashCommandBuilder()
	.setName("config")
	.setDescription("Configure the bot")
	.setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("view")
			.setDescription("View the current configuration")
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("edit")
			.setDescription("Edit a configuration value")
			.addChannelOption((option) =>
				option
					.setName("logging_channel")
					.setDescription("The channel to log to")
					.addChannelTypes(ChannelType.GuildText)
			)
			.addBooleanOption((option) =>
				option
					.setName("log_member_joins")
					.setDescription("Whether to log when a member joins")
			)
			.addBooleanOption((option) =>
				option
					.setName("log_member_leaves")
					.setDescription("Whether to log when a member leaves")
			)
			.addBooleanOption((option) =>
				option
					.setName("log_message_deletion")
					.setDescription(
						"Whether to log when a message gets deleted"
					)
			)
			.addBooleanOption((option) =>
				option
					.setName("log_message_edits")
					.setDescription("Whether to log when a message is edited")
			)
			.addBooleanOption((option) =>
				option
					.setName("log_member_banned")
					.setDescription("Whether to log when a member gets banned")
			)
			.addBooleanOption((option) =>
				option
					.setName("log_member_unbanned")
					.setDescription(
						"Whether to log when a member gets unbanned"
					)
			)
			.addBooleanOption((option) =>
				option
					.setName("log_member_kicked")
					.setDescription("Whether to log when a member gets kicked")
			)
			.addBooleanOption((option) =>
				option
					.setName("log_invite_added")
					.setDescription("Whether to log when an invite is created")
			)
			.addBooleanOption((option) =>
				option
					.setName("log_invite_removed")
					.setDescription("Whether to log when an invite is deleted")
			)
			.addBooleanOption((option) =>
				option
					.setName("sticky_roles")
					.setDescription(
						"Make certain roles *stick* to a member when leaving and rejoining"
					)
			)
			.addBooleanOption((option) =>
				option
					.setName("auto_roles")
					.setDescription("Automatically assign roles to new members")
			)
			.addBooleanOption((option) =>
				option
					.setName("owner_only")
					.setDescription(
						"Whether the owner is the only member allowed to edit the config"
					)
			)
			.addStringOption((option) =>
				option
					.setName("join_message")
					.setDescription(
						"Custom user join message. Use {user} and {server} as a placeholder"
					)
					.setMaxLength(1000)
			)
			.addStringOption((option) =>
				option
					.setName("join_direct_message")
					.setDescription(
						"Custom user join private message. Use {user} and {server} as a placeholder"
					)
					.setMaxLength(1000)
			)
			.addChannelOption((option) =>
				option
					.setName("join_message_channel")
					.setDescription(
						"The channel where join messages will be sent"
					)
					.addChannelTypes(ChannelType.GuildText)
			)
			.addStringOption((option) =>
				option
					.setName("leave_message")
					.setDescription(
						"Custom user leave message. Use {user} and {server} as a placeholder"
					)
					.setMaxLength(1000)
			)
			.addChannelOption((option) =>
				option
					.setName("leave_message_channel")
					.setDescription(
						"The channel where leave messages will be sent"
					)
					.addChannelTypes(ChannelType.GuildText)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("add_auto_role")
			.setDescription("Add an auto role")
			.addRoleOption((option) =>
				option
					.setName("role")
					.setDescription(
						"The role you want to be automatically assigned to new members"
					)
					.setRequired(true)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("remove_auto_role")
			.setDescription("Remove an auto role")
			.addRoleOption((option) =>
				option
					.setName("role")
					.setDescription(
						"The role you no longer want to be automatically assigned to new members"
					)
					.setRequired(true)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("add_sticky_role")
			.setDescription("Add an sticky role")
			.addRoleOption((option) =>
				option
					.setName("role")
					.setDescription(
						"The role you want to be automatically reassigned to returning members"
					)
					.setRequired(true)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("remove_sticky_role")
			.setDescription("Remove an sticky role")
			.addRoleOption((option) =>
				option
					.setName("role")
					.setDescription(
						"The role you no longer want to be automatically reassigned to returning members"
					)
					.setRequired(true)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("counting")
			.setDescription("Edit the counting configuration")
			.addChannelOption((option) =>
				option
					.setName("channel")
					.setDescription("The channel you want to count in")
					.addChannelTypes(ChannelType.GuildText)
					.setRequired(true)
			)
			.addStringOption((option) =>
				option
					.setName("topic")
					.setDescription(
						"An dynamic channel topic. Use {highscore} as a placeholder"
					)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("reset")
			.setDescription("Reset the configuration to the default values")
			.addStringOption((option) =>
				option
					.setName("option")
					.setDescription("The option to reset")
					.setRequired(true)
					.addChoices(
						{ name: "everything", value: "everything" },
						{ name: "logging", value: "logging" },
						{ name: "sticky_roles", value: "sticky_roles" },
						{ name: "auto_roles", value: "auto_roles" },
						{ name: "owner_only", value: "owner_only" },
						{ name: "counting", value: "counting" },
						{
							name: "join_leave_messages",
							value: "join_leave_messages"
						}
					)
			)
	);

export const customNamespace = "config";

export async function onCommand(
	_bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const { options } = interaction;

	if (interaction.guild === null) {
		interaction.reply({
			content: "Sorry, this command can only be used in a server.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	const guildId = interaction.guild.id;
	const userid = interaction.user.id;
	const isOwner = interaction.guild.ownerId === userid;
	const guild = await getGuild(guildId);

	// TODO: make sure after every change made to the guild, the guild gets saved

	if (guild.owner_only && !isOwner) {
		const embed = new EmbedBuilder()
			.setTitle("Configuration Locked")
			.setDescription(
				"The configuration can only be edited by the owner."
			)
			.setColor(0x202225);

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	if (options.getSubcommand() === "view") {
		const logging_channel = guild.logging_channel;
		const log_member_joins = guild.log_member_joins;
		const log_member_leaves = guild.log_member_leaves;
		const log_message_deletion = guild.log_message_deletion;
		const log_message_edits = guild.log_message_edits;
		const log_member_banned = guild.log_member_banned;
		const log_member_unbanned = guild.log_member_unbanned;
		const log_member_kicked = guild.log_member_kicked;
		const log_invite_added = guild.log_invite_added;
		const log_invite_removed = guild.log_invite_removed;
		const use_sticky_roles = guild.use_sticky_roles;
		const sticky_roles = guild.sticky_roles;
		const use_auto_roles = guild.use_auto_roles;
		const auto_roles = guild.auto_roles;
		const owner_only = guild.owner_only;
		const counting_channel = guild.counting_channel;
		const counting_topic = guild.counting_topic;
		const join_message = guild.join_message;
		const join_direct_message = guild.join_direct_message;
		const join_message_channel = guild.join_message_channel;
		const leave_message = guild.leave_message;
		const leave_message_channel = guild.leave_message_channel;

		// WARNING: default values have been changed

		const embed = new EmbedBuilder()
			.setTitle("Current Configuration")
			.addFields(
				{
					name: "logging_channel",
					value:
						logging_channel === null
							? "Not Configured"
							: channelMention(logging_channel),
					inline: true
				},
				{
					name: "log_member_joins",
					value: log_member_joins ? "True" : "False",
					inline: true
				},
				{
					name: "log_member_leaves",
					value: log_member_leaves ? "True" : "False",
					inline: true
				},
				{
					name: "log_message_deletion",
					value: log_message_deletion ? "True" : "False",
					inline: true
				},
				{
					name: "log_message_edits",
					value: log_message_edits ? "True" : "False",
					inline: true
				},
				{
					name: "log_member_banned",
					value: log_member_banned ? "True" : "False",
					inline: true
				},
				{
					name: "log_member_unbanned",
					value: log_member_unbanned ? "True" : "False",
					inline: true
				},
				{
					name: "log_member_kicked",
					value: log_member_kicked ? "True" : "False",
					inline: true
				},
				{
					name: "log_invite_added",
					value: log_invite_added ? "True" : "False",
					inline: true
				},
				{
					name: "log_invite_removed",
					value: log_invite_removed ? "True" : "False",
					inline: true
				},
				{
					name: "sticky_roles",
					value: use_sticky_roles
						? sticky_roles.length > 0
							? sticky_roles
									.map((roleId) => `${roleMention(roleId)}`)
									.join("\n")
							: "None"
						: "Not Enabled",
					inline: true
				},
				{
					name: "auto_roles",
					value: use_auto_roles
						? auto_roles.length > 0
							? auto_roles
									.map((roleId) => `${roleMention(roleId)}`)
									.join("\n")
							: "none"
						: "Not Enabled",
					inline: true
				},
				{
					name: "owner_only",
					value: owner_only ? "True" : "False",
					inline: true
				},
				{
					name: "counting_channel",
					value:
						counting_channel === null
							? "Not Configured"
							: channelMention(counting_channel),
					inline: true
				},
				{
					name: "counting_topic",
					value:
						counting_topic === null
							? "Not Configured"
							: counting_topic,
					inline: true
				},
				{
					name: "join_message",
					value:
						join_message === null ? "Not Configured" : join_message,
					inline: true
				},
				{
					name: "join_direct_message",
					value:
						join_direct_message === null
							? "Not Configured"
							: join_direct_message,
					inline: true
				},
				{
					name: "join_message_channel",
					value:
						join_message_channel === null
							? "Not Configured"
							: channelMention(join_message_channel),
					inline: true
				},
				{
					name: "leave_message",
					value:
						leave_message === null
							? "Not Configured"
							: leave_message,
					inline: true
				},
				{
					name: "leave_message_channel",
					value:
						leave_message_channel === null
							? "Not Configured"
							: channelMention(leave_message_channel),
					inline: true
				}
			)
			.setColor(0x202225);

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "edit") {
		const logging_channel = options.getChannel("logging_channel");
		const log_member_joins = options.getBoolean("log_member_joins");
		const log_member_leaves = options.getBoolean("log_member_leaves");
		const log_message_deletion = options.getBoolean("log_message_deletion");
		const log_message_edits = options.getBoolean("log_message_edits");
		const log_member_banned = options.getBoolean("log_member_banned");
		const log_member_unbanned = options.getBoolean("log_member_unbanned");
		const log_member_kicked = options.getBoolean("log_member_kicked");
		const log_invite_added = options.getBoolean("log_invite_added");
		const log_invite_removed = options.getBoolean("log_invite_removed");
		const sticky_roles = options.getBoolean("sticky_roles");
		const auto_roles = options.getBoolean("auto_roles");
		const owner_only = options.getBoolean("owner_only");
		const join_message = options.getString("join_message");
		const join_direct_message = options.getString("join_direct_message");
		const join_message_channel = options.getChannel("join_message_channel");
		const leave_message = options.getString("leave_message");
		const leave_message_channel = options.getChannel(
			"leave_message_channel"
		);

		if (logging_channel !== null) {
			guild.set("logging_channel", logging_channel.id);
		}
		if (log_member_joins !== null) {
			guild.set("log_member_joins", log_member_joins);
		}
		if (log_member_leaves !== null) {
			guild.set("log_member_leaves", log_member_leaves);
		}
		if (log_message_deletion !== null) {
			guild.set("log_message_deletion", log_message_deletion);
		}
		if (log_message_edits !== null) {
			guild.set("log_message_edits", log_message_edits);
		}
		if (log_member_banned !== null) {
			guild.set("log_member_banned", log_member_banned);
		}
		if (log_member_unbanned !== null) {
			guild.set("log_member_unbanned", log_member_unbanned);
		}
		if (log_member_kicked !== null) {
			guild.set("log_member_kicked", log_member_kicked);
		}
		if (log_invite_added !== null) {
			guild.set("log_invite_added", log_invite_added);
		}
		if (log_invite_removed !== null) {
			guild.set("log_invite_removed", log_invite_removed);
		}
		if (sticky_roles !== null) {
			guild.set("use_sticky_roles", sticky_roles);
		}
		if (auto_roles !== null) {
			guild.set("use_auto_roles", auto_roles);
		}
		if (owner_only !== null) {
			guild.set("owner_only", owner_only);
		}
		if (join_message !== null) {
			guild.set("join_message", join_message);
		}
		if (join_direct_message !== null) {
			guild.set("join_direct_message", join_direct_message);
		}
		if (join_message_channel !== null) {
			guild.set("join_message_channel", join_message_channel.id);
		}
		if (leave_message !== null) {
			guild.set("leave_message", leave_message);
		}
		if (leave_message_channel !== null) {
			guild.set("leave_message_channel", leave_message_channel.id);
		}

		const embed = new EmbedBuilder()
			.setTitle("Configuration Edited")
			.setDescription(
				"The configuration has been updated for this server."
			)
			.setColor(0x202225);

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "reset") {
		const option = options.getString("option");

		const embed = new EmbedBuilder()
			.setTitle("Configuration Reset")
			.setColor(0x202225);

		if (option === "everything") {
			await resetGuildToDefaults(guildId);

			embed.setDescription(
				"The configuration has been reset to its default values."
			);
		} else if (option === "logging") {
			resetGuildToDefaults(guildId, (key) =>
				[
					"logging_channel",
					"log_member_joins",
					"log_member_leaves",
					"log_message_deletion",
					"log_message_edits",
					"log_member_banned",
					"log_member_unbanned",
					"log_member_kicked",
					"log_invite_added",
					"log_invite_removed"
				].includes(key)
			);

			embed.setDescription(
				`The ${inlineCode(
					"logging"
				)} options in the configuration has been reset to its default values.`
			);
		} else if (option === "sticky_roles") {
			resetGuildToDefaults(guildId, (key) =>
				["use_sticky_roles", "sticky_roles"].includes(key)
			);

			embed.setDescription(
				`The ${inlineCode(
					"sticky_roles"
				)} option in the configuration has been reset to its default value.`
			);
		} else if (option === "auto_roles") {
			resetGuildToDefaults(guildId, (key) =>
				["use_auto_roles", "auto_roles"].includes(key)
			);

			embed.setDescription(
				`The ${inlineCode(
					"auto_roles"
				)} option in the configuration has been reset to its default value.`
			);
		} else if (option === "owner_only") {
			resetGuildToDefaults(guildId, (key) =>
				["owner_only"].includes(key)
			);

			embed.setDescription(
				`The ${inlineCode(
					"owner_only"
				)} option in the configuration has been reset to its default value.`
			);
		} else if (option === "counting") {
			resetGuildToDefaults(guildId, (key) =>
				["counting_channel", "counting_topic"].includes(key)
			);

			embed.setDescription(
				`The ${inlineCode(
					"counting"
				)} options in the configuration has been reset to its default value.`
			);
		} else if (option === "join_leave_messages") {
			resetGuildToDefaults(guildId, (key) =>
				[
					"join_message",
					"join_direct_message",
					"join_message_channel",
					"leave_message",
					"leave_message_channel"
				].includes(key)
			);

			embed.setDescription(
				`The ${inlineCode(
					"join_leave_messages"
				)} options in the configuration has been reset to its default value.`
			);
		}

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "add_auto_role") {
		const role = options.getRole("role");
		const usingAutoRoles = guild.get("use_auto_roles");

		if (!usingAutoRoles) {
			const embed = new EmbedBuilder()
				.setTitle("Configuration Conflict")
				.setDescription(
					`${inlineCode(
						"auto_roles"
					)} must be enabled in the config to add any auto roles`
				)
				.setColor(0x202225);

			interaction.reply({
				embeds: [embed],
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		const autoRoles = guild.get("auto_roles");
		if (!autoRoles.includes(role.id)) autoRoles.push(role.id);
		guild.set("auto_roles", autoRoles);

		const embed = new EmbedBuilder()
			.setTitle("Configuration Edited")
			.setDescription(
				`${inlineCode(
					role.name
				)} has been added to the list of auto roles`
			)
			.setColor(0x202225);

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "remove_auto_role") {
		const role = options.getRole("role");
		const usingAutoRoles = guild.get("use_auto_roles");

		if (!usingAutoRoles) {
			const embed = new EmbedBuilder()
				.setTitle("Configuration Conflict")
				.setDescription(
					`${inlineCode(
						"auto_roles"
					)} must be enabled in the config to remove any auto roles`
				)
				.setColor(0x202225);

			interaction.reply({
				embeds: [embed],
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		let autoRoles = guild.get("auto_roles");
		autoRoles = autoRoles.filter((roleId) => roleId !== role.id);
		guild.set("auto_roles", autoRoles);

		const embed = new EmbedBuilder()
			.setTitle("Configuration Edited")
			.setDescription(
				`${inlineCode(
					role.name
				)} has been removed to the list of auto roles`
			)
			.setColor(0x202225);

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "add_sticky_role") {
		const role = options.getRole("role");
		const usingStickyRoles = guild.get("use_sticky_roles");

		if (!usingStickyRoles) {
			const embed = new EmbedBuilder()
				.setTitle("Configuration Conflict")
				.setDescription(
					`${inlineCode(
						"sticky_roles"
					)} must be enabled in the config to add any sticky roles`
				)
				.setColor(0x202225);

			interaction.reply({
				embeds: [embed],
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		const stickyRoles = guild.get("sticky_roles");
		if (!stickyRoles.includes(role.id)) stickyRoles.push(role.id);
		guild.set("sticky_roles", stickyRoles);

		const embed = new EmbedBuilder()
			.setTitle("Configuration Edited")
			.setDescription(
				`${inlineCode(
					role.name
				)} has been added to the list of sticky roles`
			)
			.setColor(0x202225);

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "remove_sticky_role") {
		const role = options.getRole("role");
		const usingStickyRoles = guild.get("use_sticky_roles");

		if (!usingStickyRoles) {
			const embed = new EmbedBuilder()
				.setTitle("Configuration Conflict")
				.setDescription(
					`${inlineCode(
						"sticky_roles"
					)} must be enabled in the config to remove any sticky roles`
				)
				.setColor(0x202225);

			interaction.reply({
				embeds: [embed],
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		let stickyRoles = guild.get("sticky_roles");
		stickyRoles = stickyRoles.filter((roleId) => roleId !== role.id);
		guild.set("sticky_roles", stickyRoles);

		const embed = new EmbedBuilder()
			.setTitle("Configuration Edited")
			.setDescription(
				`${inlineCode(
					role.name
				)} has been removed to the list of sticky roles`
			)
			.setColor(0x202225);

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "counting") {
		const channel = options.getChannel("channel");
		const topic = options.getString("topic");

		guild.set("counting_channel", channel.id);

		if (topic == null) {
			resetGuildToDefaults(guildId, (key) => key === "counting_topic");
		} else {
			guild.set("counting_topic", topic);
		}

		const embed = new EmbedBuilder()
			.setTitle("Counting configuration has been set")
			.setColor(0x202225);

		interaction.reply({
			embeds: [embed],
			flags: MessageFlags.Ephemeral
		});
	}
}
