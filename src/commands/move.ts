import {
	SlashCommandBuilder,
	PermissionFlagsBits,
	ChannelType,
	channelMention,
	userMention,
	type ChatInputCommandInteraction,
	type Client,
	MessageFlags
} from "discord.js";

export const data = new SlashCommandBuilder()
	.setName("move")
	.setDescription("Manage users in a voice channel")
	.setDefaultMemberPermissions(PermissionFlagsBits.MoveMembers)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("me")
			.setDescription("Move yourself to a voice channel")
			.addChannelOption((option) =>
				option
					.setName("channel")
					.setDescription("The voice channel to move to")
					.addChannelTypes(ChannelType.GuildVoice)
					.setRequired(true)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("all")
			.setDescription("Move all users in a voice channel")
			.addChannelOption((option) =>
				option
					.setName("channel")
					.setDescription("The voice channel to move to")
					.addChannelTypes(ChannelType.GuildVoice)
					.setRequired(true)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("pull")
			.setDescription("Move all users in one voice channel to another")
			.addChannelOption((option) =>
				option
					.setName("channel_from")
					.setDescription("The voice channel to move from")
					.addChannelTypes(ChannelType.GuildVoice)
					.setRequired(true)
			)
			.addChannelOption((option) =>
				option
					.setName("channel_to")
					.setDescription("The voice channel to move to")
					.addChannelTypes(ChannelType.GuildVoice)
					.setRequired(true)
			)
	)
	.addSubcommand((subcommand) =>
		subcommand
			.setName("drag")
			.setDescription("Move a user to another voice channel")
			.addUserOption((option) =>
				option
					.setName("user")
					.setDescription("The user to move")
					.setRequired(true)
			)
			.addChannelOption((option) =>
				option
					.setName("channel")
					.setDescription("The voice channel to move to")
					.addChannelTypes(ChannelType.GuildVoice)
					.setRequired(true)
			)
	);

export async function onCommand(
	_bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const { options, member } = interaction;

	if (interaction.guild === null) {
		interaction.reply({
			content: "Sorry, this command can only be used in a server.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	if (
		interaction.guild.members.me === null ||
		!interaction.guild.members.me.permissions.has("MoveMembers")
	) {
		interaction.reply({
			content: "Sorry, I don't have permission to move members.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	if (options.getSubcommand() === "me") {
		const channel = options.getChannel("channel");

		if (!channel) {
			interaction.reply({
				content: "Sorry, I couldn't find that channel.",
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		if (!member.voice.channel) {
			interaction.reply({
				content: "You have to be in a voice channel.",
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		try {
			member.voice.setChannel(channel);
		} catch (error) {
			console.error("Failed to move user.", error);

			interaction.reply({
				content: `Sorry, I wasn't able to move you into ${channelMention(
					channel.id
				)}.`,
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		interaction.reply({
			content: `You have been moved into ${channelMention(channel.id)}`,
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "all") {
		const channel = options.getChannel("channel");

		if (!channel) {
			interaction.reply({
				content: "Sorry, I couldn't find that channel.",
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		const members = await interaction.guild.members.fetch();
		const voiceMembers = members.filter(
			(m) => m.voice.channel && !(m.voice.channel.id === channel.id)
		);

		for (const member of voiceMembers) {
			try {
				member.voice.setChannel(channel);
			} catch (error) {
				console.error("Failed to move user in bulk operation.", error);
			}
		}

		interaction.reply({
			content: `Moved ${voiceMembers.size} ${
				voiceMembers.size === 1 ? "user" : "users"
			} into ${channelMention(channel.id)}`,
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "pull") {
		const channelFrom = options.getChannel("channel_from");
		const channelTo = options.getChannel("channel_to");

		if (!channelFrom || !channelTo) {
			interaction.reply({
				content: "Sorry, I couldn't find that channel.",
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		const members = await interaction.guild.members.fetch();
		const voiceMembers = members.filter(
			(m) => m.voice.channel && m.voice.channel.id === channelFrom.id
		);

		for (const member of voiceMembers) {
			try {
				member.voice.setChannel(channelTo);
			} catch (error) {
				console.error("Failed to move user in bulk operation.", error);
			}
		}

		interaction.reply({
			content: `Moved ${voiceMembers.size} ${
				voiceMembers.size === 1 ? "user" : "users"
			} from ${channelMention(channelFrom.id)} into ${channelMention(
				channelTo.id
			)}`,
			flags: MessageFlags.Ephemeral
		});
	} else if (options.getSubcommand() === "drag") {
		const user = options.getUser("user");
		const channel = options.getChannel("channel");

		const userMember = interaction.guild.members.cache.get(user.id);

		if (!userMember) {
			interaction.reply({
				content: "Sorry, I couldn't find that user.",
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		if (!userMember.voice.channel) {
			interaction.reply({
				content: `${userMention(user.id)} is not in a voice channel.`,
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		try {
			userMember.voice.setChannel(channel);
		} catch (error) {
			console.error("Failed to move user.", error);

			interaction.reply({
				content: `Sorry, I wasn't able to move ${userMention(
					user.id
				)} into ${channelMention(channel.id)}.`,
				flags: MessageFlags.Ephemeral
			});
			return;
		}

		interaction.reply({
			content: `${userMention(
				user.id
			)} has been moved into ${channelMention(channel.id)}`,
			flags: MessageFlags.Ephemeral
		});
	}
}
