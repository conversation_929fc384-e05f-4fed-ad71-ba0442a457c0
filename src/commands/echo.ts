import {
	SlashCommandBuilder,
	ChannelType,
	type Client,
	type ChatInputCommandInteraction,
	MessageFlags,
	TextChannel
} from "discord.js";

export const data = new SlashCommandBuilder()
	.setName("echo")
	.setDescription("Makes the bot say anything you want")
	.addStringOption((option) =>
		option
			.setName("message")
			.setDescription("The message")
			.setRequired(true)
			.setMaxLength(2000)
	)
	.addChannelOption((option) =>
		option
			.setName("channel")
			.setDescription("The text channel")
			.addChannelTypes(ChannelType.GuildText)
	);

export async function onCommand(
	_bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const { options } = interaction;

	const message = options.getString("message");
	const channel = options.getChannel("channel") ?? interaction.channel;

	if (message === null) {
		interaction.reply({
			content: "Please provide a message.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	if (channel instanceof TextChannel === false) {
		interaction.reply({
			content: "Sorry, I can't send messages in this channel.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	await interaction.deferReply({
		// content: "Sending message...",
		flags: MessageFlags.Ephemeral
	});

	channel
		.send({
			content: message
		})
		.then(() => {
			interaction.editReply({
				content: "Message has been sent!"
			});
		});
}
