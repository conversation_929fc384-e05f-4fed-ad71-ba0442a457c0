import {
	ActionRowBuilder,
	ButtonBuilder,
	ButtonStyle,
	roleMention,
	PermissionFlagsBits,
	SlashCommandBuilder,
	type CacheType,
	type Client,
	type Interaction,
	type ChatInputCommandInteraction,
	MessageFlags,
	ButtonInteraction
} from "discord.js";

export const data = new SlashCommandBuilder()
	.setName("roleprompt")
	.setDescription("Create button to assign a role to a user")
	.setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
	.addStringOption((option) =>
		option
			.setName("message")
			.setDescription("The message regarding the role prompt")
			.setRequired(true)
			.setMaxLength(2000)
	)
	.addStringOption((option) =>
		option
			.setName("label")
			.setDescription("The label of the button")
			.setRequired(true)
			.setMaxLength(20)
	)
	.addRoleOption((option) =>
		option
			.setName("role")
			.setDescription("The role to add to the user")
			.setRequired(true)
	)
	.addStringOption((option) =>
		option
			.setName("color")
			.setDescription("The color of the button")
			.addChoices(
				{ name: "Blue", value: "blue" },
				{ name: "Gray", value: "gray" },
				{ name: "Green", value: "green" },
				{ name: "Red", value: "red" }
			)
	);

export const customNamespace = "roleprompt";

export function onCommand(
	_bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const { options } = interaction;

	const message = options.getString("message");
	const label = options.getString("label");
	const role = options.getRole("role");
	const color = options.getString("color");

	if (message === null) {
		interaction.reply({
			content: "Please provide a message.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}
	if (label === null) {
		interaction.reply({
			content: "Please provide a label.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}
	if (role === null) {
		interaction.reply({
			content: "Please provide a role.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	if (role.rawPosition === 0) {
		interaction.reply({
			content: "You can't assign @everyone to a role.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	let buttonStyle: ButtonStyle;

	switch (color) {
		case "blue":
			buttonStyle = ButtonStyle.Primary;
			break;
		case "gray":
			buttonStyle = ButtonStyle.Secondary;
			break;
		case "green":
			buttonStyle = ButtonStyle.Success;
			break;
		case "red":
			buttonStyle = ButtonStyle.Danger;
			break;
		default:
			buttonStyle = ButtonStyle.Primary;
	}

	const row = new ActionRowBuilder().addComponents(
		new ButtonBuilder()
			.setCustomId(`roleprompt:${role.id}`)
			.setLabel(label)
			.setStyle(buttonStyle)
	);

	interaction.reply({
		content: message,
		components: [row]
	});
}

export async function onButton(
	_bot: Client<true>,
	interaction: ButtonInteraction
) {
	if (!interaction.guild.members.me.permissions.has("MANAGE_ROLES")) {
		interaction.reply({
			content: "Sorry, I don't have permission to assign roles.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	const roleId = /roleprompt:([0-9]+)/.exec(interaction.customId)[1];
	const role = interaction.member.roles.cache.find((r) => r.id === roleId);

	try {
		if (role) {
			await interaction.member.roles.remove(role);

			interaction.reply({
				content: `:regional_indicator_x: Role unassigned ${roleMention(
					roleId
				)}`,
				flags: MessageFlags.Ephemeral
			});
		} else {
			await interaction.member.roles.add(roleId);

			interaction.reply({
				content: `:white_check_mark: Role assigned ${roleMention(
					roleId
				)}`,
				flags: MessageFlags.Ephemeral
			});
		}
	} catch (error) {
		console.error("Failed to assign role in role prompt.", error);

		interaction.reply({
			content: `Sorry, I wasn't able to assign the role.`,
			flags: MessageFlags.Ephemeral
		});
	}
}
