import {
	type ChatInputCommandInteraction,
	type Client,
	MessageFlags,
	SlashCommandBuilder
} from "discord.js";

export const data = new SlashCommandBuilder()
	.setName("clap")
	.setDescription("👏 Add 👏 claps 👏 to 👏 your 👏 message 👏")
	.addStringOption((option) =>
		option
			.setName("message")
			.setDescription("The message to clap")
			.setRequired(true)
			.setMaxLength(2000)
	);

export function onCommand(
	_bot: Client<true>,
	interaction: ChatInputCommandInteraction
) {
	const { options } = interaction;

	let message = options.getString("message");

	if (message === null) {
		interaction.reply({
			content: "Please provide a message.",
			flags: MessageFlags.Ephemeral
		});
		return;
	}

	message = `👏 ${message.replaceAll(" ", " 👏 ")} 👏`;
	message = message.substring(0, 2000);

	interaction.reply({
		content: message
	});
}
