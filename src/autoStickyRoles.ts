import type { Client, GuildMember } from "discord.js";
import { getGuild } from "./data/database.ts";

async function isUsingAutoRoles(guildId: string) {
	return await getGuild(guildId).then((guild) => guild.use_auto_roles);
}

async function getAutoRoles(guildId: string) {
	return await getGuild(guildId).then((guild) => guild.auto_roles);
}

async function isUsingStickyRoles(guildId: string) {
	return await getGuild(guildId).then((guild) => guild.use_sticky_roles);
}

async function getUsersPreviousRoles(bot: Client<true>, member: GuildMember) {
	const guildData = bot.DataManager.getGuildData(member.guild.id);
	return guildData[`sticky_roles:${member.user.id}`] ?? [];
}

async function getStickyRoles(guildId: string) {
	return await getGuild(guildId).then((guild) => guild.sticky_roles);
}

export default (bot: Client<true>) => {
	bot.on("guildMemberAdd", async (member) => {
		let newRoles: string[] = [];

		if (await isUsingAutoRoles(member.guild.id)) {
			newRoles = newRoles.concat(await getAutoRoles(member.guild.id));
		}
		if (await isUsingStickyRoles(member.guild.id)) {
			const previousRoles = await getUsersPreviousRoles(bot, member);
			const stickyRoles = await getStickyRoles(member.guild.id);
			const roles = previousRoles.filter((roleId) =>
				stickyRoles.includes(roleId)
			);

			newRoles = newRoles.concat(roles);
		}

		if (newRoles.length > 0) {
			member.roles.add(newRoles);
		}
	});
	bot.on("guildMemberRemove", async (member) => {
		if (!(await isUsingStickyRoles(member.guild.id))) return;

		const stickyRoles = await getStickyRoles(member.guild.id);
		const roles = member._roles.filter((roleId) =>
			stickyRoles.includes(roleId)
		);

		const guildData = bot.DataManager.getGuildData(member.guild.id);
		guildData[`sticky_roles:${member.user.id}`] = roles;
		bot.DataManager.setGuildData(member.guild.id, guildData);
	});
};
