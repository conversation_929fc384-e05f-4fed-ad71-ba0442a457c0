import { REST } from "@discordjs/rest";
import {
	Collection,
	type Client,
	type ChatInputCommandInteraction,
	type APIApplicationCommandOption,
	type SlashCommandBuilder,
	type ButtonInteraction,
	type ModalSubmitInteraction
} from "discord.js";
import { Routes } from "discord-api-types/v10";
import fs from "node:fs";
import { BOT_TOKEN } from "./data/config.ts";
import process from "node:process";

type AppCommand = {
	data: SlashCommandBuilder;
	customNamespace?: string;
	onCommand?: (
		bot: Client<true>,
		interaction: ChatInputCommandInteraction
	) => Promise<void> | void;
	onButton?: (
		bot: Client<true>,
		interaction: ButtonInteraction
	) => Promise<void> | void;
	onModal?: (
		bot: Client<true>,
		interaction: ModalSubmitInteraction
	) => Promise<void> | void;
};

const appCommandsPayload: APIApplicationCommandOption[] = [];
const commands = new Collection<string, AppCommand>();
const customNamespaces = new Map<string, string>();

for (const file of fs
	.readdirSync(`${process.cwd()}/src/commands/`)
	.filter((file) => file.endsWith(".js"))) {
	const command = (await import(`./commands/${file}`)) as AppCommand;
	const commandName = command.data.name;

	appCommandsPayload.push(command.data.toJSON());
	commands.set(commandName, command);

	if (typeof command.customNamespace === "string") {
		customNamespaces.set(command.customNamespace, commandName);
	}
}

const rest = new REST({ version: "10" }).setToken(BOT_TOKEN);

export default async (bot: Client<true>) => {
	// Register application slash commands
	console.log("Started refreshing application slash commands.");

	await rest.put(Routes.applicationCommands(bot.user.id), {
		// NOTE: this line can throw
		body: appCommandsPayload
	});

	console.log("Successfully reloaded application slash commands.");

	// Add an interaction create event listener
	bot.on("interactionCreate", async (interaction) => {
		if (typeof interaction.guildId !== "string") return;

		if (interaction.isChatInputCommand()) {
			const { commandName } = interaction;

			if (typeof commandName !== "string") return;

			const command = commands.get(commandName);

			if (!command) return;
			if (typeof command.onCommand === "undefined") return;

			await command.onCommand(bot, interaction);
		} else if (interaction.isButton()) {
			const customId = interaction.customId;
			if (typeof customId !== "string") return;

			const namespace = customId.substring(0, customId.indexOf(":"));
			const commandName = customNamespaces.get(namespace);

			if (!commandName) return;

			const command = commands.get(commandName);

			if (!command) return;
			if (typeof command.onButton === "undefined") return;

			await command.onButton(bot, interaction);
		} else if (interaction.isModalSubmit()) {
			const customId = interaction.customId;
			if (typeof customId !== "string") return;

			const namespace = customId.substring(0, customId.indexOf(":"));
			const commandName = customNamespaces.get(namespace);

			if (!commandName) return;

			const command = commands.get(commandName);

			if (!command) return;
			if (typeof command.onModal === "undefined") return;

			await command.onModal(bot, interaction);
		}
	});
};
