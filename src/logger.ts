import {
	EmbedBuilder,
	userMention,
	time,
	AuditLogEvent,
	bold,
	channelMention,
	Guild,
	type Client,
	type User,
	TextChannel
} from "discord.js";
import { getGuild } from "./data/database.ts";

async function getLoggingChannel(guild: Guild): Promise<TextChannel | null> {
	const channelId = await getGuild(guild.id).then(
		(guild) => guild.logging_channel
	);
	if (!channelId) return null;

	const channel = guild.channels.cache.get(channelId);
	if (channel instanceof TextChannel === false) return null;

	return channel;
}

async function isLogged(
	guildid: string,
	key:
		| "member_joins"
		| "member_leaves"
		| "message_deletion"
		| "message_edits"
		| "member_banned"
		| "member_unbanned"
		| "member_kicked"
		| "invite_added"
		| "invite_removed"
) {
	return await getGuild(guildid).then((guild) => guild[`log_${key}`]);
}

export default (bot: Client<true>) => {
	bot.on("messageDelete", async (message) => {
		if (message.guild === null || !message.author) return;
		if (message.author.bot) return;
		if (message.content === null) return;

		const loggingChannel = await getLoggingChannel(message.guild);
		if (loggingChannel == null) return;

		if (!(await isLogged(message.guild.id, "message_deletion"))) return;

		// Check audit logs for more info
		const fetchedLogs = await message.guild
			.fetchAuditLogs({
				limit: 6,
				type: AuditLogEvent.MessageDelete
			})
			.catch(console.error);
		if (!fetchedLogs) return;

		// Get the executor from the audit logs
		let auditExecutor = "Unknown";

		for (const entry of fetchedLogs.entries) {
			if (
				Date.now() - entry.createdTimestamp < 20000 &&
				entry.target.id === message.author.id &&
				entry.extra.channel.id === message.channel.id
			) {
				if (entry?.executor)
					auditExecutor = userMention(entry.executor.id);
				break;
			}
		}

		// Create message embed
		const embed = new EmbedBuilder()
			.setColor(0xf25656)
			.setTitle("Message Deleted")
			.addFields(
				{ name: "Sent By", value: userMention(message.author.id) },
				{
					name: "Time Sent",
					value: time(
						Math.floor(message.createdTimestamp / 1000),
						"f"
					)
				},
				{ name: "Message Content", value: message.content },
				{
					name: "Deleted By",
					value: auditExecutor
				}
			)
			.setTimestamp();

		// Send message in logging channel
		loggingChannel
			.send({
				embeds: [embed]
			})
			.catch(console.error);
	});

	bot.on("messageUpdate", async (oldMessage, newMessage) => {
		if (newMessage.guild === null) return;

		const loggingChannel = await getLoggingChannel(newMessage.guild);
		if (loggingChannel == null) return;

		if (!(await isLogged(newMessage.guild.id, "message_edits"))) return;

		if (newMessage.author.bot) return;
		if (oldMessage.content === newMessage.content) return; // Only log if the message content changes

		const editedTimestamp =
			newMessage.editedTimestamp ??
			oldMessage.editedTimestamp ??
			Date.now();

		// Create message embed
		const embed = new EmbedBuilder()
			.setColor(0xed861f)
			.setTitle("Message Edited")
			.addFields(
				{ name: "Sent By", value: userMention(newMessage.author.id) },
				{
					name: "Time Edited",
					value: time(Math.floor(editedTimestamp / 1000), "f")
				},
				{ name: "New Content", value: newMessage.content }
			)
			.setTimestamp();

		if (oldMessage.content !== null) {
			// TODO: show the difference not the exact old text
			embed.addFields({
				name: "Old Content",
				value: oldMessage.content
			});
		}

		// Send message in logging channel
		loggingChannel
			.send({
				embeds: [embed]
			})
			.catch(console.error);
	});

	bot.on("guildMemberAdd", async (member) => {
		const loggingChannel = await getLoggingChannel(member.guild);
		if (loggingChannel == null) return;

		if (!(await isLogged(member.guild.id, "member_joins"))) return;

		const joinedTimestamp = member.joinedTimestamp ?? Date.now();

		// Create message embed
		const embed = new EmbedBuilder()
			.setColor(0x58f257)
			.setTitle("Member Joined")
			.addFields(
				{
					name: "User Profile",
					value: [
						`${bold("Profile")}: ${userMention(member.user.id)}`,
						writeUserTagname(member.user),
						`${bold("User ID")}: ${member.user.id}`
					].join("\n")
				},
				{
					name: "Time Joined",
					value: time(Math.floor(joinedTimestamp / 1000), "f")
				}
			)
			.setThumbnail(
				`https://cdn.discordapp.com/avatars/${member.user.id}/${member.user.avatar}.png`
			)
			.setTimestamp();

		// Send message in logging channel
		loggingChannel
			.send({
				embeds: [embed]
			})
			.catch(console.error);
	});

	bot.on("guildMemberRemove", async (member) => {
		const loggingChannel = await getLoggingChannel(member.guild);
		if (loggingChannel == null) return;

		// Check audit logs for more info
		const fetchedLogs = await member.guild
			.fetchAuditLogs({
				limit: 6,
				type: AuditLogEvent.MemberKick
			})
			.catch(console.error);
		if (!fetchedLogs) return;

		// Get the executor from the audit logs
		let auditReason = "Unspecified";
		let auditExecutor = "Unknown";

		for (const entry of fetchedLogs.entries) {
			if (
				Date.now() - entry.createdTimestamp < 20000 &&
				entry.target.id === member.user.id
			) {
				if (entry?.reason !== null) auditReason = entry.reason;
				if (entry?.executor)
					auditExecutor = userMention(entry.executor.id);
				break;
			}
		}

		if (await isLogged(member.guild.id, "member_kicked")) {
			// Create message embed
			const embed = new EmbedBuilder()
				.setColor(0xf7682a)
				.setTitle("Member Kicked")
				.addFields({
					name: "User Profile",
					value: [
						`${bold("Profile")}: ${userMention(member.user.id)}`,
						writeUserTagname(member.user),
						`${bold("User ID")}: ${member.user.id}`
					].join("\n")
				})
				.setThumbnail(
					`https://cdn.discordapp.com/avatars/${member.user.id}/${member.user.avatar}.png`
				)
				.setTimestamp();

			// Add additional audit log info
			if (member.joinedTimestamp)
				embed.addFields({
					name: "Member Since",
					value: time(Math.floor(member.joinedTimestamp / 1000), "f")
				});

			if (auditReason)
				embed.addFields({
					name: "Reason",
					value: auditReason
				});

			if (auditExecutor)
				embed.addFields({
					name: "Kicked By",
					value: auditExecutor
				});

			// Send message in logging channel
			loggingChannel
				.send({
					embeds: [embed]
				})
				.catch(console.error);
		} else if (await isLogged(member.guild.id, "member_leaves")) {
			// Create message embed
			const embed = new EmbedBuilder()
				.setColor(0xf25656)
				.setTitle("Member Left")
				.addFields({
					name: "User Profile",
					value: [
						`${bold("Profile")}: ${userMention(member.user.id)}`,
						writeUserTagname(member.user),
						`${bold("User ID")}: ${member.user.id}`
					].join("\n")
				})
				.setThumbnail(
					`https://cdn.discordapp.com/avatars/${member.user.id}/${member.user.avatar}.png`
				)
				.setTimestamp();

			// Add additional audit log info
			if (member.joinedTimestamp)
				embed.addFields({
					name: "Member Since",
					value: time(Math.floor(member.joinedTimestamp / 1000), "f")
				});

			// Send message in logging channel
			loggingChannel
				.send({
					embeds: [embed]
				})
				.catch(console.error);
		}
	});

	bot.on("guildBanAdd", async (ban) => {
		const loggingChannel = await getLoggingChannel(ban.guild);
		if (loggingChannel == null) return;

		if (!(await isLogged(ban.guild.id, "member_banned"))) return;

		// Check audit logs for more info
		const fetchedLogs = await ban.guild
			.fetchAuditLogs({
				limit: 6,
				type: AuditLogEvent.MemberBanAdd
			})
			.catch(console.error);
		if (!fetchedLogs) return;

		// Get the executor from the audit logs
		let auditReason = "Unspecified";
		let auditExecutor = "Unknown";

		for (const entry of fetchedLogs.entries) {
			if (
				Date.now() - entry.createdTimestamp < 20000 &&
				entry.target.id === ban.user.id
			) {
				if (entry?.reason !== null) auditReason = entry.reason;
				if (entry?.executor)
					auditExecutor = userMention(entry.executor.id);
				break;
			}
		}

		// Create message embed
		const embed = new EmbedBuilder()
			.setColor(0xc23761)
			.setTitle("Member Banned")
			.addFields({
				name: "User Profile",
				value: [
					`${bold("Profile")}: ${userMention(ban.user.id)}`,
					writeUserTagname(ban.user),
					`${bold("User ID")}: ${ban.user.id}`
				].join("\n")
			})
			.setThumbnail(
				`https://cdn.discordapp.com/avatars/${ban.user.id}/${ban.user.avatar}.png`
			)
			.setTimestamp();

		// Add additional audit log info
		if (auditReason)
			embed.addFields({
				name: "Reason",
				value: auditReason
			});

		if (auditExecutor)
			embed.addFields({
				name: "Banned By",
				value: auditExecutor
			});

		// Send message in logging channel
		loggingChannel
			.send({
				embeds: [embed]
			})
			.catch(console.error);
	});

	bot.on("guildBanRemove", async (ban) => {
		const loggingChannel = await getLoggingChannel(ban.guild);
		if (loggingChannel == null) return;

		if (!(await isLogged(ban.guild.id, "member_unbanned"))) return;

		// Check audit logs for more info
		const fetchedLogs = await ban.guild
			.fetchAuditLogs({
				limit: 6,
				type: AuditLogEvent.MemberBanRemove
			})
			.catch(console.error);
		if (!fetchedLogs) return;

		// Get the executor from the audit logs
		let auditExecutor = "Unknown";

		for (const entry of fetchedLogs.entries) {
			if (
				Date.now() - entry.createdTimestamp < 20000 &&
				entry.target.id === ban.user.id
			) {
				if (entry?.executor)
					auditExecutor = userMention(entry.executor.id);
				break;
			}
		}

		// Create message embed
		const embed = new EmbedBuilder()
			.setColor(0x24c98a)
			.setTitle("Member Unbanned")
			.addFields({
				name: "User Profile",
				value: [
					`${bold("Profile")}: ${userMention(ban.user.id)}`,
					writeUserTagname(ban.user),
					`${bold("User ID")}: ${ban.user.id}`
				].join("\n")
			})
			.setThumbnail(
				`https://cdn.discordapp.com/avatars/${ban.user.id}/${ban.user.avatar}.png`
			)
			.setTimestamp();

		// Add additional audit log info
		if (auditExecutor)
			embed.addFields({
				name: "Unbanned By",
				value: auditExecutor
			});

		// Send message in logging channel
		loggingChannel
			.send({
				embeds: [embed]
			})
			.catch(console.error);
	});

	bot.on("inviteCreate", async (invite) => {
		if (invite.guild instanceof Guild === false) return;

		const loggingChannel = await getLoggingChannel(invite.guild);
		if (loggingChannel == null) return;

		if (!(await isLogged(invite.guild.id, "invite_added"))) return;

		// Create message embed
		const embed = new EmbedBuilder()
			.setColor(0xf062f0)
			.setTitle("Invite Created")
			.addFields(
				{ name: "Code", value: invite.code },
				{ name: "Inviter", value: userMention(invite.inviterId) },
				{ name: "Channel", value: channelMention(invite.channelId) },
				{
					name: "Expires",
					value:
						invite._expiresTimestamp == null
							? "Never"
							: time(
									Math.floor(invite._expiresTimestamp / 1000),
									"R"
							  )
				},
				{
					name: "Max Uses",
					value:
						invite.maxUses === 0 ? "No Limit" : `${invite.maxUses}`
				},
				{
					name: "Created At",
					value: time(Math.floor(invite.createdTimestamp / 1000), "f")
				}
			)
			.setTimestamp();

		// Send message in logging channel
		loggingChannel
			.send({
				embeds: [embed]
			})
			.catch(console.error);
	});

	bot.on("inviteDelete", async (invite) => {
		if (invite.guild instanceof Guild === false) return;

		const loggingChannel = await getLoggingChannel(invite.guild);
		if (loggingChannel == null) return;

		if (!(await isLogged(invite.guild.id, "invite_removed"))) return;

		// Check audit logs for more info
		const fetchedLogs = await invite.guild
			.fetchAuditLogs({
				limit: 6,
				type: AuditLogEvent.InviteDelete
			})
			.catch(console.error);

		const auditEntry = fetchedLogs.entries
			.filter((a) => {
				return (
					Date.now() - a.createdTimestamp < 20000 &&
					a.target.code === invite.code &&
					a.target.channelId === invite.channelId
				);
			})
			.first();

		// Create message embed
		const embed = new EmbedBuilder()
			.setColor(0x585ef2)
			.setTitle("Invite Removed")
			.addFields(
				{ name: "Code", value: invite.code },
				{ name: "Channel", value: channelMention(invite.channelId) }
			)
			.setTimestamp();

		// Add additional audit log info
		if (auditEntry) {
			embed.addFields({
				name: "Removed By",
				value: userMention(auditEntry.executor.id)
			});
		}

		// Send message in logging channel
		loggingChannel
			.send({
				embeds: [embed]
			})
			.catch(console.error);
	});

	// bot.on("channelPinsUpdate", async (channel, time) => {
	//     console.log(channel, time)
	// });

	console.log("Logging module has been loaded.");
};

function writeUserTagname(user: User) {
	if (!user.discriminator) {
		// TODO: test this
		return `${bold("Username")}: ${user.username}`;
	}

	return `${bold("User Tag")}: ${user.username}#${user.discriminator}`;
}
