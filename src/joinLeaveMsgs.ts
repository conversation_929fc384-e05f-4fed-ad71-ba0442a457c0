import {
	type Client,
	type Guild,
	type GuildMember,
	TextChannel,
	type User,
	userMention
} from "discord.js";
import { getGuild } from "./data/database.ts";

async function getJoinMsgChannel(guild: Guild): Promise<TextChannel | null> {
	const channelId = await getGuild(guild.id).then(
		(guild) => guild.join_message_channel
	);
	if (channelId == null) return null;

	const channel = guild.channels.cache.get(channelId);
	if (channel instanceof TextChannel === false) return null;

	return channel;
}

async function getLeaveMsgChannel(guild: Guild) {
	const channelId = await getGuild(guild.id).then(
		(guild) => guild.leave_message_channel
	);
	if (channelId == null) return null;

	const channel = guild.channels.cache.get(channelId);
	if (channel instanceof TextChannel === false) return null;

	return channel;
}

async function getJoinMsg(member: GuildMember) {
	const msg = await getGuild(member.guild.id).then(
		(guild) => guild.join_message
	);
	if (!msg) return null;

	return msg
		.replace(/{user}/g, userMention(member.user.id))
		.replace(/{server}/g, member.guild.name);
}

async function getJoinPrivateMsg(member: GuildMember) {
	const msg = await getGuild(member.guild.id).then(
		(guild) => guild.join_direct_message
	);
	if (!msg) return null;

	return msg
		.replace(/{user}/g, userMention(member.user.id))
		.replace(/{server}/g, member.guild.name);
}

async function getLeaveMsg<T>(
	member: T & { guild: { id: string; name: string }; user: User }
) {
	const msg = await getGuild(member.guild.id).then(
		(guild) => guild.leave_message
	);
	if (!msg) return null;

	return msg
		.replace(/{user}/g, userTagFormat(member.user))
		.replace(/{server}/g, member.guild.name);
}

function userTagFormat(user: User) {
	return user.discriminator === "0"
		? user.username
		: `${user.username}#${user.discriminator}`;
}

export default (bot: Client<true>) => {
	bot.on("guildMemberAdd", async (member) => {
		const joinMsgChannel = await getJoinMsgChannel(member.guild);
		const joinMsg = await getJoinMsg(member);
		const joinPrivateMsg = await getJoinPrivateMsg(member);

		if (joinMsgChannel !== null && typeof joinMsg === "string") {
			joinMsgChannel.send(joinMsg).catch(console.error);
		}
		if (typeof joinPrivateMsg === "string") {
			member.send(joinPrivateMsg).catch(console.error);
		}
	});
	bot.on("guildMemberRemove", async (member) => {
		const leaveMsgChannel = await getLeaveMsgChannel(member.guild);
		const leaveMsg = await getLeaveMsg(member);

		if (leaveMsgChannel !== null && typeof leaveMsg === "string") {
			leaveMsgChannel.send(leaveMsg).catch(console.error);
		}
	});
};
