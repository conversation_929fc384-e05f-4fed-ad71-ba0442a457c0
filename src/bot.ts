import { Client, GatewayIntentBits, ActivityType } from "discord.js";
import { botConfig } from "./data/config.ts";
import initCmd from "./cmd.ts";
import initLogger from "./logger.ts";
import initCounting from "./counting.ts";
import initJoinLeaveMsgs from "./joinLeaveMsgs.ts";
import initAutoStickyRoles from "./autoStickyRoles.ts";
import { initializeDatabase } from "./data/database.ts";
import { BOT_TOKEN } from "./data/config.ts";

const { custom_activity } = botConfig;

const bot: Client<true> = new Client({
	// Assume the the client was able to login, and throw an error later if not
	intents: [
		GatewayIntentBits.Guilds,
		GatewayIntentBits.GuildMembers,
		GatewayIntentBits.GuildMessages,
		GatewayIntentBits.GuildVoiceStates,
		GatewayIntentBits.GuildBans,
		GatewayIntentBits.GuildModeration,
		GatewayIntentBits.MessageContent,
		GatewayIntentBits.GuildInvites
	]
});

bot.on("ready", async () => {
	// Throw an error if the bot failed to login
	if (!bot.user) {
		throw new Error("Failed to login");
	}

	// Initialize database tables
	await initializeDatabase();

	// Initialize slash commands
	await initCmd(bot);

	// Start bot modules
	initLogger(bot);
	initCounting(bot);
	initJoinLeaveMsgs(bot);
	initAutoStickyRoles(bot);

	// Set bot status and activity
	bot.user.setStatus(custom_activity.status);

	const updateActivity = () => {
		const actIndex = Math.floor(
			Math.random() * custom_activity.activities.length
		);
		const activity = custom_activity.activities[actIndex];

		if (
			typeof activity === "object" &&
			typeof activity?.type === "string"
		) {
			activity.type = ActivityType[activity.type];
		}

		bot.user.setActivity(activity);
	};

	updateActivity();
	setInterval(updateActivity, custom_activity.interval);

	// Log bot startup
	console.log(`Logged in as ${bot.user.tag}!`);
});

bot.login(BOT_TOKEN);
