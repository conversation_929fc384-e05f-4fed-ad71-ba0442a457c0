import {
	type Client,
	EmbedBuilder,
	type Guild,
	type GuildBasedChannel,
	inlineCode,
	type User
} from "discord.js";
import { botConfig } from "./data/config.ts";
import { getGuild } from "./data/database.ts";

const { emojis } = botConfig;

async function getCountingChannel(
	guild: Guild
): Promise<GuildBasedChannel | null> {
	const channelId = await getGuild(guild.id).then(
		(guild) => guild.counting_channel
	);
	if (!channelId) return null;

	const channel = guild.channels.cache.get(channelId);
	return !channel ? null : channel;
}

async function getCountingTopic(guildId: string) {
	return await getGuild(guildId).then((guild) => guild.counting_topic);
}

function userTagFormat(user: User) {
	return user.discriminator === "0"
		? user.username
		: `${user.username}#${user.discriminator}`;
}

export default (bot: Client<true>) => {
	bot.on("messageCreate", async (message) => {
		if (!message.guild) return;
		if (!message.member) return;

		const countingChannel = await getCountingChannel(message.guild);
		const countingTopic = await getCountingTopic(message.guild.id);
		if (countingChannel == null) return;

		if (message.channel.id !== countingChannel.id) return;
		if (message.author.bot) return;

		const data = bot.DataManager.getGuildData(message.guild.id);

		if (typeof data.counting_number === "undefined")
			data.counting_number = 0;
		if (typeof data.counting_last_guesser === "undefined")
			data.counting_last_guesser = bot.user.id;
		if (typeof data.counting_high_score === "undefined")
			data.counting_high_score = 0;

		const number = Number(message.content);

		if (!Number.isNaN(number)) {
			if (data.counting_last_guesser !== message.member.user.id) {
				// If guesser wasn't the old guesser
				if (number - 1 === data.counting_number) {
					// If number was the next number
					data.counting_number = number; // Set the new number
					data.counting_last_guesser = message.member.user.id; // Set the last guesser

					if (number > data.counting_high_score) {
						// Set the new highscore
						data.counting_high_score = number;
					}

					bot.DataManager.setGuildData(message.guild.id, data); // Save guild data
					if (number === 100) {
						message.react("💯").catch(console.error);
					} else {
						message.react(emojis.check_mark).catch(console.error);
					}
				} else {
					// If number wasn't the next number
					message.reply(
						"You've messed up the counting! The current number has been reset to 0."
					);
					data.counting_number = 0; // Reset counting
					data.counting_last_guesser = null; // Reset last guesser

					bot.DataManager.setGuildData(message.guild.id, data); // Save guild data
					message.react(emojis.cross_mark).catch(console.error);
				}
			} else {
				// If guesser was the old guesser
				message.reply(
					"It isn't your turn! The current number is now 0."
				);
				data.counting_number = 0; // Reset counting
				data.counting_last_guesser = null; // Reset last guesser

				bot.DataManager.setGuildData(message.guild.id, data); // Save guild data
				message.react(emojis.cross_mark).catch(console.error);
			}

			if (typeof countingTopic === "string") {
				const customTopic = countingTopic.replace(
					/{highscore}/g,
					data.counting_high_score
				);

				if (message.channel.topic !== customTopic) {
					message.channel.setTopic(customTopic).catch(console.error);
				}
			}
		}
	});
	bot.on("messageUpdate", async (oldMessage, newMessage) => {
		const countingChannel = await getCountingChannel(oldMessage.guild);
		if (countingChannel == null) return;

		if (oldMessage.channel.id !== countingChannel.id) return;
		if (oldMessage.author.bot) return;

		const data = bot.DataManager.getGuildData(oldMessage.guild.id);
		const number = Number(oldMessage.content);

		if (typeof data.counting_number === "undefined")
			data.counting_number = 0;

		if (!Number.isNaN(number) && number === data.counting_number) {
			const embed = new EmbedBuilder()
				.setColor(0xed861f)
				.setTitle("**Number Edited!**")
				.setDescription(`Number: ${inlineCode(oldMessage.content)}`)
				.setFooter({
					text: `Sent by ${userTagFormat(oldMessage.member.user)}`
				});

			countingChannel
				.send({
					embeds: [embed]
				})
				.then((sentEmbed) => {
					sentEmbed.react(emojis.check_mark).catch(console.error);
				})
				.catch(console.error);
		}
	});
	bot.on("messageDelete", async (message) => {
		if (!message.guild) return;
		if (!message.author) return;
		if (!message.member) return;
		if (message.author.bot) return;

		const countingChannel = await getCountingChannel(message.guild);

		if (countingChannel == null) return;
		if (message.channel.id !== countingChannel.id) return;

		const data = bot.DataManager.getGuildData(message.guild.id);
		const number = Number(message.content);

		if (typeof data.counting_number === "undefined")
			data.counting_number = 0;

		if (!Number.isNaN(number) && number === data.counting_number) {
			const embed = new EmbedBuilder()
				.setColor(0xf25656)
				.setTitle("**Number Deleted!**")
				.setDescription(`Number: ${inlineCode(message.content)}`)
				.setFooter({
					text: `Sent by ${userTagFormat(message.member.user)}`
				});

			countingChannel
				.send({
					embeds: [embed]
				})
				.then((sentEmbed) => {
					sentEmbed.react(emojis.check_mark).catch(console.error);
				})
				.catch(console.error);
		}
	});
};
