{"version": "5", "specifiers": {"jsr:@std/assert@1": "1.0.12", "jsr:@std/internal@^1.0.6": "1.0.6", "npm:@discordjs/builders@^1.10.1": "1.10.1", "npm:@discordjs/rest@^2.4.3": "2.4.3", "npm:@libsql/kysely-libsql@~0.4.1": "0.4.1_kys<PERSON>@0.28.2", "npm:@types/node@*": "22.12.0", "npm:discord-api-types@~0.37.120": "0.37.120", "npm:discord.js@^14.18.0": "14.18.0", "npm:kysely@~0.28.2": "0.28.2", "npm:mongoose@^8.13.2": "8.13.2"}, "jsr": {"@std/assert@1.0.12": {"integrity": "08009f0926dda9cbd8bef3a35d3b6a4b964b0ab5c3e140a4e0351fbf34af5b9a", "dependencies": ["jsr:@std/internal"]}, "@std/internal@1.0.6": {"integrity": "9533b128f230f73bd209408bb07a4b12f8d4255ab2a4d22a1fd6d87304aca9a4"}}, "npm": {"@discordjs/builders@1.10.1": {"integrity": "sha512-OWo1fY4ztL1/M/DUyRPShB4d/EzVfuUvPTRRHRIt/YxBrUYSz0a+JicD5F5zHFoNs2oTuWavxCOVFV1UljHTng==", "dependencies": ["@discordjs/formatters", "@discordjs/util", "@sapphire/shapeshift", "discord-api-types@0.37.119", "fast-deep-equal", "ts-mixer", "tslib"]}, "@discordjs/collection@1.5.3": {"integrity": "sha512-SVb428OMd3WO1paV3rm6tSjM4wC+Kecaa1EUGX7vc6/fddvw/6lg90z4QtCqm21zvVe92vMMDt9+DkIvjXImQQ=="}, "@discordjs/collection@2.1.1": {"integrity": "sha512-Li<PERSON>usze9Tc7qF03sLCujF5iZp7K+vRNEDBZ86FT9aQAv3vxMLihUvKvpsCWiQ2DJq1tVckopKm1rxomgNUc9hg=="}, "@discordjs/formatters@0.6.0": {"integrity": "sha512-YIruKw4UILt/ivO4uISmrGq2GdMY6EkoTtD0oS0GvkJFRZbTSdPhzYiUILbJ/QslsvC9H9nTgGgnarnIl4jMfw==", "dependencies": ["discord-api-types@0.37.119"]}, "@discordjs/rest@2.4.3": {"integrity": "sha512-+SO4RKvWsM+y8uFHgYQrcTl/3+cY02uQOH7/7bKbVZsTfrfpoE62o5p+mmV+s7FVhTX82/kQUGGbu4YlV60RtA==", "dependencies": ["@discordjs/collection@2.1.1", "@discordjs/util", "@sapphire/async-queue", "@sapphire/snowflake@3.5.5", "@vladfrangu/async_event_emitter", "discord-api-types@0.37.119", "magic-bytes.js", "tslib", "undici"]}, "@discordjs/util@1.1.1": {"integrity": "sha512-eddz6UnOBEB1oITPinyrB2Pttej49M9FZQY8NxgEvc3tq6ZICZ19m70RsmzRdDHk80O9NoYN/25AqJl8vPVf/g=="}, "@discordjs/ws@1.2.1": {"integrity": "sha512-PBvenhZG56a6tMWF/f4P6f4GxZKJTBG95n7aiGSPTnodmz4N5g60t79rSIAq7ywMbv8A4jFtexMruH+oe51aQQ==", "dependencies": ["@discordjs/collection@2.1.1", "@discordjs/rest", "@discordjs/util", "@sapphire/async-queue", "@types/ws", "@vladfrangu/async_event_emitter", "discord-api-types@0.37.119", "tslib", "ws"]}, "@libsql/client@0.8.1": {"integrity": "sha512-xGg0F4iTDFpeBZ0r4pA6icGsYa5rG6RAG+i/iLDnpCAnSuTqEWMDdPlVseiq4Z/91lWI9jvvKKiKpovqJ1kZWA==", "dependencies": ["@libsql/core", "@libsql/hrana-client", "js-base64", "libsql", "promise-limit"]}, "@libsql/core@0.8.1": {"integrity": "sha512-u6nrj6HZMTPsgJ9EBhLzO2uhqhlHQJQmVHV+0yFLvfGf3oSP8w7TjZCNUgu1G8jHISx6KFi7bmcrdXW9lRt++A==", "dependencies": ["js-base64"]}, "@libsql/darwin-arm64@0.3.19": {"integrity": "sha512-rmOqsLcDI65zzxlUOoEiPJLhqmbFsZF6p4UJQ2kMqB+Kc0Rt5/A1OAdOZ/Wo8fQfJWjR1IbkbpEINFioyKf+nQ==", "os": ["darwin"], "cpu": ["arm64"]}, "@libsql/darwin-x64@0.3.19": {"integrity": "sha512-q9O55B646zU+644SMmOQL3FIfpmEvdWpRpzubwFc2trsa+zoBlSkHuzU9v/C+UNoPHQVRMP7KQctJ455I/h/xw==", "os": ["darwin"], "cpu": ["x64"]}, "@libsql/hrana-client@0.6.2": {"integrity": "sha512-MWxgD7mXLNf9FXXiM0bc90wCjZSpErWKr5mGza7ERy2FJNNMXd7JIOv+DepBA1FQTIfI8TFO4/QDYgaQC0goNw==", "dependencies": ["@libsql/isomorphic-fetch", "@libsql/isomorphic-ws", "js-base64", "node-fetch"]}, "@libsql/isomorphic-fetch@0.2.5": {"integrity": "sha512-8s/B2TClEHms2yb+JGpsVRTPBfy1ih/Pq6h6gvyaNcYnMVJvgQRY7wAa8U2nD0dppbCuDU5evTNMEhrQ17ZKKg=="}, "@libsql/isomorphic-ws@0.1.5": {"integrity": "sha512-DtLWIH29onUYR00i0GlQ3UdcTRC6EP4u9w/h9LxpUZJWRMARk6dQwZ6Jkd+QdwVpuAOrdxt18v0K2uIYR3fwFg==", "dependencies": ["@types/ws", "ws"]}, "@libsql/kysely-libsql@0.4.1_kysely@0.28.2": {"integrity": "sha512-mCTa6OWgoME8LNu22COM6XjKBmcMAvNtIO6DYM10jSAFq779fVlrTKQEmXIB8TwJVU65dA5jGCpT8gkDdWS0HQ==", "dependencies": ["@libsql/client", "kys<PERSON>"]}, "@libsql/linux-arm64-gnu@0.3.19": {"integrity": "sha512-mgeAUU1oqqh57k7I3cQyU6Trpdsdt607eFyEmH5QO7dv303ti+LjUvh1pp21QWV6WX7wZyjeJV1/VzEImB+jRg==", "os": ["linux"], "cpu": ["arm64"]}, "@libsql/linux-arm64-musl@0.3.19": {"integrity": "sha512-VEZtxghyK6zwGzU9PHohvNxthruSxBEnRrX7BSL5jQ62tN4n2JNepJ6SdzXp70pdzTfwroOj/eMwiPt94gkVRg==", "os": ["linux"], "cpu": ["arm64"]}, "@libsql/linux-x64-gnu@0.3.19": {"integrity": "sha512-2t/J7LD5w2f63wGihEO+0GxfTyYIyLGEvTFEsMO16XI5o7IS9vcSHrxsvAJs4w2Pf907uDjmc7fUfMg6L82BrQ==", "os": ["linux"], "cpu": ["x64"]}, "@libsql/linux-x64-musl@0.3.19": {"integrity": "sha512-BLsXyJaL8gZD8+3W2LU08lDEd9MIgGds0yPy5iNPp8tfhXx3pV/Fge2GErN0FC+nzt4DYQtjL+A9GUMglQefXQ==", "os": ["linux"], "cpu": ["x64"]}, "@libsql/win32-x64-msvc@0.3.19": {"integrity": "sha512-ay1X9AobE4BpzG0XPw1gplyLZPGHIgJOovvW23gUrukRegiUP62uzhpRbKNogLlUOynyXeq//prHgPXiebUfWg==", "os": ["win32"], "cpu": ["x64"]}, "@mongodb-js/saslprep@1.2.2": {"integrity": "sha512-EB0O3SCSNRUFk66iRCpI+cXzIjdswfCs7F6nOC3RAGJ7xr5YhaicvsRwJ9eyzYvYRlCSDUO/c7g4yNulxKC1WA==", "dependencies": ["sparse-bitfield"]}, "@neon-rs/load@0.0.4": {"integrity": "sha512-kTPhdZyTQxB+2wpiRcFWrDcejc4JI6tkPuS7UZCG4l6Zvc5kU/gGQ/ozvHTh1XR5tS+UlfAfGuPajjzQjCiHCw=="}, "@sapphire/async-queue@1.5.5": {"integrity": "sha512-cvGzxbba6sav2zZkH8GPf2oGk9yYoD5qrNWdu9fRehifgnFZJMV+nuy2nON2roRO4yQQ+v7MK/Pktl/HgfsUXg=="}, "@sapphire/shapeshift@4.0.0": {"integrity": "sha512-d9dUmWVA7MMiKobL3VpLF8P2aeanRTu6ypG2OIaEv/ZHH/SUQ2iHOVyi5wAPjQ+HmnMuL0whK9ez8I/raWbtIg==", "dependencies": ["fast-deep-equal", "lodash"]}, "@sapphire/snowflake@3.5.3": {"integrity": "sha512-jjmJywLAFoWeBi1W7994zZyiNWPIiqRRNAmSERxyg93xRGzNYvGjlZ0gR6x0F4gPRi2+0O6S71kOZYyr3cxaIQ=="}, "@sapphire/snowflake@3.5.5": {"integrity": "sha512-xzvBr1Q1c4lCe7i6sRnrofxeO1QTP/LKQ6A6qy0iB4x5yfiSfARMEQEghojzTNALDTcv8En04qYNIco9/K9eZQ=="}, "@types/node@22.12.0": {"integrity": "sha512-Fll2FZ1riMjNmlmJOdAyY5pUbkftXslB5DgEzlIuNaiWhXd00FhWxVC/r4yV/4wBb9JfImTu+jiSvXTkJ7F/gA==", "dependencies": ["undici-types"]}, "@types/webidl-conversions@7.0.3": {"integrity": "sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA=="}, "@types/whatwg-url@11.0.5": {"integrity": "sha512-coYR071JRaHa+xoEvvYqvnIHaVqaYrLPbsufM9BF63HkwI5Lgmy2QR8Q5K/lYDYo5AK82wOvSOS0UsLTpTG7uQ==", "dependencies": ["@types/webidl-conversions"]}, "@types/ws@8.18.1": {"integrity": "sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==", "dependencies": ["@types/node"]}, "@vladfrangu/async_event_emitter@2.4.6": {"integrity": "sha512-Ra<PERSON>5qZo6D2CVS6sTHFKg1v5Ohq/+Bo2LZ5gzUEwZ/WkHhwtGTCB/sVLw8ijOkAUxasZ+WshN/Rzj4ywsABJ5ZA=="}, "bson@6.10.3": {"integrity": "sha512-MTxGsqgYTwfshYWTRdmZRC+M7FnG1b4y7RO7p2k3X24Wq0yv1m77Wsj0BzlPzd/IowgESfsruQCUToa7vbOpPQ=="}, "data-uri-to-buffer@4.0.1": {"integrity": "sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A=="}, "debug@4.4.0": {"integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "dependencies": ["ms"]}, "detect-libc@2.0.2": {"integrity": "sha512-UX6sGumvvqSaXgdKGUsgZWqcUyIXZ/vZTrlRT/iobiKhGL0zL4d3osHj3uqllWJK+i+sixDS/3COVEOFbupFyw=="}, "discord-api-types@0.37.119": {"integrity": "sha512-WasbGFXEB+VQWXlo6IpW3oUv73Yuau1Ig4AZF/m13tXcTKnMpc/mHjpztIlz4+BM9FG9BHQkEXiPto3bKduQUg=="}, "discord-api-types@0.37.120": {"integrity": "sha512-7xpNK0EiWjjDFp2nAhHXezE4OUWm7s1zhc/UXXN6hnFFU8dfoPHgV0Hx0RPiCa3ILRpdeh152icc68DGCyXYIw=="}, "discord.js@14.18.0": {"integrity": "sha512-SvU5kVUvwunQhN2/+0t55QW/1EHfB1lp0TtLZUSXVHDmyHTrdOj5LRKdR0zLcybaA15F+NtdWuWmGOX9lE+CAw==", "dependencies": ["@discordjs/builders", "@discordjs/collection@1.5.3", "@discordjs/formatters", "@discordjs/rest", "@discordjs/util", "@discordjs/ws", "@sapphire/snowflake@3.5.3", "discord-api-types@0.37.119", "fast-deep-equal", "lodash.snakecase", "tslib", "undici"]}, "fast-deep-equal@3.1.3": {"integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "fetch-blob@3.2.0": {"integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "dependencies": ["node-domexception", "web-streams-polyfill"]}, "formdata-polyfill@4.0.10": {"integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "dependencies": ["fetch-blob"]}, "js-base64@3.7.7": {"integrity": "sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw=="}, "kareem@2.6.3": {"integrity": "sha512-C3iHfuGUXK2u8/ipq9LfjFfXFxAZMQJJq7vLS45r3D9Y2xQ/m4S8zaR4zMLFWh9AsNPXmcFfUDhTEO8UIC/V6Q=="}, "kysely@0.28.2": {"integrity": "sha512-4YAVLoF0Sf0UTqlhgQMFU9iQECdah7n+13ANkiuVfRvlK+uI0Etbgd7bVP36dKlG+NXWbhGua8vnGt+sdhvT7A=="}, "libsql@0.3.19": {"integrity": "sha512-Aj5cQ5uk/6fHdmeW0TiXK42FqUlwx7ytmMLPSaUQPin5HKKKuUPD62MAbN4OEweGBBI7q1BekoEN4gPUEL6MZA==", "dependencies": ["@neon-rs/load", "detect-libc"], "optionalDependencies": ["@libsql/darwin-arm64", "@libsql/darwin-x64", "@libsql/linux-arm64-gnu", "@libsql/linux-arm64-musl", "@libsql/linux-x64-gnu", "@libsql/linux-x64-musl", "@libsql/win32-x64-msvc"], "os": ["darwin", "linux", "win32"], "cpu": ["x64", "arm64", "wasm32"]}, "lodash.snakecase@4.1.1": {"integrity": "sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw=="}, "lodash@4.17.21": {"integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="}, "magic-bytes.js@1.10.0": {"integrity": "sha512-/k20Lg2q8LE5xiaaSkMXk4sfvI+9EGEykFS4b0CHHGWqDYU0bGUFSwchNOMA56D7TCs9GwVTkqe9als1/ns8UQ=="}, "memory-pager@1.5.0": {"integrity": "sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg=="}, "mongodb-connection-string-url@3.0.2": {"integrity": "sha512-rMO7CGo/9BFwyZABcKAWL8UJwH/Kc2x0g72uhDWzG48URRax5TCIcJ7Rc3RZqffZzO/Gwff/jyKwCU9TN8gehA==", "dependencies": ["@types/whatwg-url", "whatwg-url"]}, "mongodb@6.15.0": {"integrity": "sha512-ifBhQ0rRzHDzqp9jAQP6OwHSH7dbYIQjD3SbJs9YYk9AikKEettW/9s/tbSFDTpXcRbF+u1aLrhHxDFaYtZpFQ==", "dependencies": ["@mongodb-js/saslprep", "bson", "mongodb-connection-string-url"]}, "mongoose@8.13.2": {"integrity": "sha512-riCBqZmNkYBWjXpM3qWLDQw7QmTKsVZDPhLXFJqC87+OjocEVpvS3dA2BPPUiLAu+m0/QmEj5pSXKhH+/DgerQ==", "dependencies": ["bson", "kareem", "mongodb", "mpath", "m<PERSON>y", "ms", "sift"]}, "mpath@0.9.0": {"integrity": "sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew=="}, "mquery@5.0.0": {"integrity": "sha512-iQMncpmEK8R8ncT8HJGsGc9Dsp8xcgYMVSbs5jgnm1lFHTZqMJTUWTDx1LBO8+mK3tPNZWFLBghQEIOULSTHZg==", "dependencies": ["debug"]}, "ms@2.1.3": {"integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node-domexception@1.0.0": {"integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "deprecated": true}, "node-fetch@3.3.2": {"integrity": "sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==", "dependencies": ["data-uri-to-buffer", "fetch-blob", "formdata-polyfill"]}, "promise-limit@2.7.0": {"integrity": "sha512-7nJ6v5lnJsXwGprnGXga4wx6d1POjvi5Qmf1ivTRxTjH4Z/9Czja/UCMLVmB9N93GeWOU93XaFaEt6jbuoagNw=="}, "punycode@2.3.1": {"integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="}, "sift@17.1.3": {"integrity": "sha512-Rtlj66/b0ICeFzYTuNvX/EF1igRbbnGSvEyT79McoZa/DeGhMyC5pWKOEsZKnpkqtSeovd5FL/bjHWC3CIIvCQ=="}, "sparse-bitfield@3.0.3": {"integrity": "sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==", "dependencies": ["memory-pager"]}, "tr46@5.1.0": {"integrity": "sha512-IUWnUK7ADYR5Sl1fZlO1INDUhVhatWl7BtJWsIhwJ0UAK7ilzzIa8uIqOO/aYVWHZPJkKbEL+362wrzoeRF7bw==", "dependencies": ["punycode"]}, "ts-mixer@6.0.4": {"integrity": "sha512-ufKpbmrugz5Aou4wcr5Wc1UUFWOLhq+Fm6qa6P0w0K5Qw2yhaUoiWszhCVuNQyNwrlGiscHOmqYoAox1PtvgjA=="}, "tslib@2.8.1": {"integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="}, "undici-types@6.20.0": {"integrity": "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg=="}, "undici@6.21.1": {"integrity": "sha512-q/1rj5D0/zayJB2FraXdaWxbhWiNKDvu8naDT2dl1yTlvJp4BLtOcp2a5BvgGNQpYYJzau7tf1WgKv3b+7mqpQ=="}, "web-streams-polyfill@3.3.3": {"integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw=="}, "webidl-conversions@7.0.0": {"integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g=="}, "whatwg-url@14.2.0": {"integrity": "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==", "dependencies": ["tr46", "webidl-conversions"]}, "ws@8.18.1": {"integrity": "sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w=="}}, "workspace": {"dependencies": ["jsr:@std/assert@1", "npm:@discordjs/builders@^1.10.1", "npm:@discordjs/rest@^2.4.3", "npm:@libsql/kysely-libsql@~0.4.1", "npm:discord-api-types@~0.37.120", "npm:discord.js@^14.18.0", "npm:kysely@~0.28.2", "npm:mongoose@^8.13.2"]}}